{"extends": ["next/core-web-vitals"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "react/no-unescaped-entities": ["error", {"forbid": ["'", "\"", ">", "}"]}], "@typescript-eslint/no-require-imports": "error"}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"]}]}