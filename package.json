{"name": "boguani-nextjs", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "heroku-postbuild": "npm run build"}, "dependencies": {"@heroicons/react": "^2.2.0", "@types/crypto-js": "^4.2.2", "@types/simple-peer": "^9.11.8", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "framer-motion": "^12.19.2", "lucide-react": "^0.525.0", "next": "15.3.4", "plaid": "^36.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-plaid-link": "^4.0.1", "simple-peer": "^9.11.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "twilio": "^5.7.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "typescript": "^5"}}