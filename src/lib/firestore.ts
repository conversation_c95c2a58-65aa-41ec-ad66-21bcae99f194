import {
  collection,
  doc,
  addDoc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';

export interface User {
  uid: string;
  phoneNumber: string;
  name: string;
  username: string;
  displayName: string;
  avatar?: string;
  status?: string;
  isOnline: boolean;
  lastSeen: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  text: string;
  type: 'text' | 'payment' | 'image' | 'voice' | 'video' | 'file';
  timestamp: Timestamp;
  status: 'sent' | 'delivered' | 'read';
  amount?: number;
  currency?: string;
  imageUrl?: string;
  fileUrl?: string;
  fileName?: string;
  fileSize?: string;
  duration?: string;
  replyTo?: string;
  isForwarded?: boolean;
  reactions?: { emoji: string; users: string[] }[];
}

export interface Chat {
  id: string;
  participants: string[];
  lastMessage: string;
  lastMessageTime: Timestamp;
  unreadCount: { [userId: string]: number };
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isGroup?: boolean;
  groupName?: string;
  groupAvatar?: string;
  adminIds?: string[];
}

export interface Contact {
  id: string;
  userId: string;
  contactUserId: string;
  name: string;
  phoneNumber: string;
  addedAt: Timestamp;
}

class FirestoreService {
  // User operations
  async createUser(userData: Omit<User, 'createdAt' | 'updatedAt' | 'lastSeen'>): Promise<void> {
    await setDoc(doc(db, 'users', userData.uid), {
      ...userData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastSeen: serverTimestamp()
    });
  }

  async getUser(uid: string): Promise<User | null> {
    const userDoc = await getDoc(doc(db, 'users', uid));
    return userDoc.exists() ? userDoc.data() as User : null;
  }

  async updateUser(uid: string, updates: Partial<User>): Promise<void> {
    await updateDoc(doc(db, 'users', uid), {
      ...updates,
      updatedAt: serverTimestamp()
    });
  }

  async getUserByUsername(username: string): Promise<User | null> {
    const q = query(collection(db, 'users'), where('username', '==', username));
    const querySnapshot = await getDocs(q);
    return querySnapshot.empty ? null : querySnapshot.docs[0].data() as User;
  }

  async searchUsers(searchTerm: string): Promise<User[]> {
    const q = query(
      collection(db, 'users'),
      where('username', '>=', searchTerm.toLowerCase()),
      where('username', '<=', searchTerm.toLowerCase() + '\uf8ff'),
      limit(10)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as User);
  }

  // Chat operations
  async createChat(participants: string[]): Promise<string> {
    const chatData = {
      participants,
      lastMessage: '',
      lastMessageTime: serverTimestamp(),
      unreadCount: participants.reduce((acc, userId) => ({ ...acc, [userId]: 0 }), {}),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const chatRef = await addDoc(collection(db, 'chats'), chatData);
    return chatRef.id;
  }

  async getChat(chatId: string): Promise<Chat | null> {
    const chatDoc = await getDoc(doc(db, 'chats', chatId));
    return chatDoc.exists() ? { id: chatDoc.id, ...chatDoc.data() } as Chat : null;
  }

  async getUserChats(userId: string): Promise<Chat[]> {
    const q = query(
      collection(db, 'chats'),
      where('participants', 'array-contains', userId),
      orderBy('lastMessageTime', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Chat));
  }

  async updateChat(chatId: string, updates: Partial<Chat>): Promise<void> {
    await updateDoc(doc(db, 'chats', chatId), {
      ...updates,
      updatedAt: serverTimestamp()
    });
  }

  // Message operations
  async sendMessage(messageData: Omit<Message, 'id' | 'timestamp' | 'status'>): Promise<string> {
    const message = {
      ...messageData,
      timestamp: serverTimestamp(),
      status: 'sent' as const
    };

    const messageRef = await addDoc(collection(db, 'messages'), message);

    // Update chat with last message
    await this.updateChat(messageData.chatId, {
      lastMessage: messageData.text,
      lastMessageTime: serverTimestamp()
    });

    return messageRef.id;
  }

  async getMessages(chatId: string, limitCount: number = 50): Promise<Message[]> {
    const q = query(
      collection(db, 'messages'),
      where('chatId', '==', chatId),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Message)).reverse();
  }

  async updateMessage(messageId: string, updates: Partial<Message>): Promise<void> {
    await updateDoc(doc(db, 'messages', messageId), updates);
  }

  async deleteMessage(messageId: string): Promise<void> {
    await deleteDoc(doc(db, 'messages', messageId));
  }

  // Contact operations
  async addContact(userId: string, contactUserId: string, name: string, phoneNumber: string): Promise<void> {
    const contactData = {
      userId,
      contactUserId,
      name,
      phoneNumber,
      addedAt: serverTimestamp()
    };

    await addDoc(collection(db, 'contacts'), contactData);
  }

  async getContacts(userId: string): Promise<Contact[]> {
    const q = query(
      collection(db, 'contacts'),
      where('userId', '==', userId),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Contact));
  }

  async removeContact(contactId: string): Promise<void> {
    await deleteDoc(doc(db, 'contacts', contactId));
  }

  // Real-time listeners
  onMessagesChange(chatId: string, callback: (messages: Message[]) => void) {
    const q = query(
      collection(db, 'messages'),
      where('chatId', '==', chatId),
      orderBy('timestamp', 'desc'),
      limit(50)
    );

    return onSnapshot(q, (snapshot) => {
      const messages = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Message)).reverse();
      callback(messages);
    });
  }

  onChatsChange(userId: string, callback: (chats: Chat[]) => void) {
    const q = query(
      collection(db, 'chats'),
      where('participants', 'array-contains', userId),
      orderBy('lastMessageTime', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const chats = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Chat));
      callback(chats);
    });
  }

  onUserStatusChange(userId: string, callback: (user: User | null) => void) {
    return onSnapshot(doc(db, 'users', userId), (snapshot) => {
      const user = snapshot.exists() ? snapshot.data() as User : null;
      callback(user);
    });
  }

  // Batch operations
  async markMessagesAsRead(chatId: string, userId: string): Promise<void> {
    const q = query(
      collection(db, 'messages'),
      where('chatId', '==', chatId),
      where('senderId', '!=', userId),
      where('status', '!=', 'read')
    );

    const querySnapshot = await getDocs(q);
    const batch = writeBatch(db);

    querySnapshot.docs.forEach(doc => {
      batch.update(doc.ref, { status: 'read' });
    });

    await batch.commit();

    // Reset unread count for this user
    const chatRef = doc(db, 'chats', chatId);
    await updateDoc(chatRef, {
      [`unreadCount.${userId}`]: 0
    });
  }
}

export const firestoreService = new FirestoreService();
export default firestoreService;
