'use client';

import { useState, useEffect } from 'react';
import { auth } from '@/lib/firebase';
import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';

export default function TestSMSPage() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [confirmationResult, setConfirmationResult] = useState<any>(null);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Initialize reCAPTCHA with better error handling
    if (typeof window !== 'undefined') {
      // Clear any existing reCAPTCHA
      if ((window as any).recaptchaVerifier) {
        try {
          (window as any).recaptchaVerifier.clear();
        } catch (e) {
          console.log('No existing reCAPTCHA to clear');
        }
      }

      // Wait a bit for DOM to be ready
      setTimeout(() => {
        try {
          console.log('Initializing reCAPTCHA...');
          const recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
            size: 'normal',
            callback: (response: any) => {
              console.log('reCAPTCHA solved:', response);
              setMessage('reCAPTCHA solved successfully - ready to send SMS');
            },
            'expired-callback': () => {
              console.log('reCAPTCHA expired');
              setMessage('reCAPTCHA expired - please refresh');
            }
          });

          (window as any).recaptchaVerifier = recaptchaVerifier;

          recaptchaVerifier.render().then(() => {
            console.log('reCAPTCHA rendered successfully');
            setMessage('reCAPTCHA loaded - solve it then enter phone number');
          }).catch((error) => {
            console.error('reCAPTCHA render error:', error);
            setMessage('reCAPTCHA failed to load: ' + error.message);
          });

        } catch (error: any) {
          console.error('reCAPTCHA setup error:', error);
          setMessage('reCAPTCHA setup failed: ' + error.message);
        }
      }, 1000);
    }
  }, []);

  const sendSMS = async () => {
    if (!phoneNumber) {
      setMessage('Please enter a phone number');
      return;
    }

    setLoading(true);
    setMessage('Sending SMS...');

    try {
      const appVerifier = (window as any).recaptchaVerifier;
      if (!appVerifier) {
        throw new Error('reCAPTCHA not initialized');
      }

      console.log('Sending SMS to:', phoneNumber);
      const confirmation = await signInWithPhoneNumber(auth, phoneNumber, appVerifier);
      
      setConfirmationResult(confirmation);
      setMessage('SMS sent successfully! Check your phone.');
      console.log('SMS sent, confirmation:', confirmation);
      
    } catch (error: any) {
      console.error('SMS error:', error);
      setMessage('SMS failed: ' + error.message + ' (Code: ' + error.code + ')');
    } finally {
      setLoading(false);
    }
  };

  const verifyCode = async () => {
    if (!verificationCode || !confirmationResult) {
      setMessage('Please enter the verification code');
      return;
    }

    setLoading(true);
    setMessage('Verifying code...');

    try {
      const result = await confirmationResult.confirm(verificationCode);
      setMessage('Phone verified successfully! User: ' + result.user.uid);
      console.log('Verification successful:', result);
      
    } catch (error: any) {
      console.error('Verification error:', error);
      setMessage('Verification failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6">Firebase SMS Test</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Phone Number</label>
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="+1234567890"
              className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white"
            />
          </div>

          <button
            onClick={sendSMS}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {loading ? 'Sending...' : 'Send SMS'}
          </button>

          {confirmationResult && (
            <>
              <div>
                <label className="block text-sm font-medium mb-2">Verification Code</label>
                <input
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  placeholder="123456"
                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white"
                />
              </div>

              <button
                onClick={verifyCode}
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                {loading ? 'Verifying...' : 'Verify Code'}
              </button>
            </>
          )}

          <div className="mt-6 p-4 bg-gray-800 rounded-lg">
            <h3 className="font-medium mb-2">Status:</h3>
            <p className="text-sm text-gray-300">{message}</p>
          </div>

          <div id="recaptcha-container" className="mt-4"></div>
        </div>

        <div className="mt-8 text-sm text-gray-400">
          <h3 className="font-medium mb-2">Debug Info:</h3>
          <p>Auth Domain: {process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}</p>
          <p>Project ID: {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}</p>
          <p>Current URL: {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
        </div>
      </div>
    </div>
  );
}
