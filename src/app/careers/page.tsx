'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';

export default function CareersPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Join Our Team</h1>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Help us build the future of value-based communication. Join a team that's revolutionizing how people connect and share value.
            </p>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Why Work With Us</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Join a mission-driven team building the future of communication
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-rocket text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Innovation First</h3>
                <p className="text-gray-300 leading-relaxed">
                  Work on cutting-edge technology that's changing how people communicate and share value globally.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-users text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Amazing Team</h3>
                <p className="text-gray-300 leading-relaxed">
                  Collaborate with talented individuals from diverse backgrounds who share your passion for excellence.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-chart-line text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Growth Opportunities</h3>
                <p className="text-gray-300 leading-relaxed">
                  Accelerate your career with continuous learning, mentorship, and leadership opportunities.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-balance-scale text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Work-Life Balance</h3>
                <p className="text-gray-300 leading-relaxed">
                  Flexible working arrangements, unlimited PTO, and comprehensive benefits for your wellbeing.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-globe text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Global Impact</h3>
                <p className="text-gray-300 leading-relaxed">
                  Make a meaningful difference in how billions of people communicate and transact worldwide.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-heart text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Mission Driven</h3>
                <p className="text-gray-300 leading-relaxed">
                  Be part of a company that values purpose, integrity, and making the world more connected.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Open Positions</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We're always looking for talented individuals to join our team
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow text-center"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                <i className="fas fa-envelope text-2xl text-gray-900"></i>
              </div>
              <h3 className="text-2xl font-bold mb-4 gold-gradient">We're Hiring!</h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                Don't see a position that fits? We're always interested in hearing from talented individuals who share our vision.
              </p>
              <Link href="/contact" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                Get In Touch
              </Link>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
