'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';

export default function DownloadsPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Download BoGuani</h1>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Get BoGuani on all your devices and start experiencing the future of value-based communication.
            </p>
          </div>
        </div>
      </section>

      {/* Download Options */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Choose Your Platform</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Available on all major platforms and devices
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fab fa-apple text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">iOS</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  Download for iPhone and iPad from the App Store.
                </p>
                <Link href="#" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  Download iOS App
                </Link>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fab fa-android text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Android</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  Download for Android devices from Google Play Store.
                </p>
                <Link href="#" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  Download Android App
                </Link>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-desktop text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Desktop</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  Download for Windows, macOS, and Linux.
                </p>
                <Link href="#" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  Download Desktop App
                </Link>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-globe text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Web App</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  Use BoGuani directly in your web browser.
                </p>
                <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  Open Web App
                </Link>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fab fa-chrome text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Browser Extension</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  Add BoGuani to your browser for quick access.
                </p>
                <Link href="#" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  Add Extension
                </Link>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-code text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Developer API</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  Integrate BoGuani into your applications.
                </p>
                <Link href="/api-docs" className="border-2 border-yellow-400 text-yellow-400 px-6 py-3 rounded-full font-semibold hover:bg-yellow-400 hover:text-purple-900 transition-all inline-block">
                  View API Docs
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
