import { NextRequest, NextResponse } from 'next/server';
import { doc, setDoc, collection, query, where, getDocs, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { name, username, phoneNumber, userToken } = await request.json();

    // Validate inputs
    if (!name || !username || !phoneNumber || !userToken) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Validate name
    if (name.length < 2 || name.length > 50) {
      return NextResponse.json(
        { error: 'Name must be between 2 and 50 characters' },
        { status: 400 }
      );
    }

    // Validate username
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      return NextResponse.json(
        { error: 'Username must be 3-20 characters and contain only letters, numbers, and underscores' },
        { status: 400 }
      );
    }

    // Check if username is available
    const usersRef = collection(db, 'users');
    const usernameQuery = query(usersRef, where('username', '==', username.toLowerCase()));
    const usernameSnapshot = await getDocs(usernameQuery);

    if (!usernameSnapshot.empty) {
      return NextResponse.json(
        { error: 'Username is already taken' },
        { status: 409 }
      );
    }

    // Generate user ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create user profile
    const userProfile = {
      uid: userId,
      phoneNumber,
      name: name.trim(),
      username: username.toLowerCase(),
      displayName: name.trim(),
      isOnline: true,
      status: 'Available',
      avatar: name.charAt(0).toUpperCase() + (name.split(' ')[1]?.charAt(0).toUpperCase() || ''),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastSeen: serverTimestamp()
    };

    // Save to Firestore
    await setDoc(doc(db, 'users', userId), userProfile);

    // Return user data (excluding sensitive information)
    return NextResponse.json({
      success: true,
      message: 'Profile created successfully',
      user: {
        uid: userId,
        phoneNumber,
        name: name.trim(),
        username: username.toLowerCase(),
        displayName: name.trim(),
        avatar: userProfile.avatar,
        status: 'Available'
      }
    });

  } catch (error: any) {
    console.error('Create profile error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to create profile' },
      { status: 500 }
    );
  }
}
