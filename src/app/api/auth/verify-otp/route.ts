import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/lib/auth';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { phoneNumber, code, sessionId } = await request.json();

    // Validate inputs
    if (!phoneNumber || !code || !sessionId) {
      return NextResponse.json(
        { error: 'Phone number, verification code, and session ID are required' },
        { status: 400 }
      );
    }

    // Validate code format
    if (!/^\d{6}$/.test(code)) {
      return NextResponse.json(
        { error: 'Invalid verification code format' },
        { status: 400 }
      );
    }

    // Access OTP storage (same as send-otp)
    const otpData = (global as any).otpStorage?.get(phoneNumber);

    if (!otpData) {
      return NextResponse.json(
        { error: 'No verification code found. Please request a new code.' },
        { status: 400 }
      );
    }

    // Check if OTP has expired
    if (Date.now() > otpData.expires) {
      (global as any).otpStorage?.delete(phoneNumber);
      return NextResponse.json(
        { error: 'Verification code has expired. Please request a new code.' },
        { status: 400 }
      );
    }

    // Verify the code
    if (code === otpData.code) {
      // Remove used OTP
      (global as any).otpStorage?.delete(phoneNumber);

      // Generate a temporary user token
      const userToken = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      return NextResponse.json({
        success: true,
        message: 'Phone number verified successfully',
        userToken,
        phoneNumber
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid verification code' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Verify OTP error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to verify code' },
      { status: 500 }
    );
  }
}
