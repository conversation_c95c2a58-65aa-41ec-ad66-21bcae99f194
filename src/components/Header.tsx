'use client';

import Link from 'next/link';
import { useState } from 'react';

interface HeaderProps {
  showNavLinks?: boolean;
  className?: string;
}

export default function Header({ showNavLinks = true, className = '' }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className={`glass-card fixed w-full z-50 professional-shadow ${className}`}>
      <div className="container mx-auto px-6 py-3 flex justify-between items-center">
        <Link href="/" className="flex items-center">
          <div className="text-yellow-400 text-3xl mr-2">
            <i className="fas fa-comment-dollar"></i>
          </div>
          <span className="font-bold text-2xl gold-gradient">BoGuani</span>
        </Link>
        
        {showNavLinks && (
          <>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="hover:text-yellow-400 transition-colors">Features</a>
              <a href="#about" className="hover:text-yellow-400 transition-colors">About</a>
              <a href="#how-it-works" className="hover:text-yellow-400 transition-colors">How It Works</a>
              <a href="#pricing" className="hover:text-yellow-400 transition-colors">Pricing</a>
              <a href="#contact" className="hover:text-yellow-400 transition-colors">Contact</a>
              <Link href="/auth" className="bg-yellow-400 text-purple-900 px-4 py-2 rounded-full font-semibold hover:bg-yellow-300 transition-colors">
                Get Started
              </Link>
            </div>
            
            <button
              className="md:hidden text-yellow-400"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
            </button>
          </>
        )}
      </div>

      {/* Mobile Menu */}
      {showNavLinks && isMenuOpen && (
        <div className="md:hidden bg-gray-900 bg-opacity-95 backdrop-blur-md">
          <div className="container mx-auto px-6 py-4 flex flex-col space-y-4">
            <a href="#features" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>Features</a>
            <a href="#about" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>About</a>
            <a href="#how-it-works" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>How It Works</a>
            <a href="#pricing" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>Pricing</a>
            <a href="#contact" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>Contact</a>
            <Link href="/auth" className="bg-yellow-400 text-purple-900 px-4 py-2 rounded-full font-semibold hover:bg-yellow-300 transition-colors text-center">
              Get Started
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
}
