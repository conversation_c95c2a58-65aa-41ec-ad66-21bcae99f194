import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    fontFamily: {
      sans: ['var(--font-sans)', 'system-ui', 'sans-serif'],
    },
    extend: {
      colors: {
        // Custom color palette
        'dark-purple': '#2D1B4E',
        'medium-purple': '#3D2A5F',
        'light-purple': '#4E3A70',
        'dark-gray': '#1E1E24',
        'medium-gray': '#2D2D34',
        'gold-light': '#F2D675',
        primary: {
          DEFAULT: '#2E003E',
          50: '#f5f0ff',
          100: '#e8d9ff',
          200: '#d4b3ff',
          300: '#b87dff',
          400: '#9a47ff',
          500: '#7d1bff',
          600: '#6a00ff',
          700: '#5a00d6',
          800: '#4a00ad',
          900: '#2E003E',
          light: '#9d7bde',
          dark: '#1f002a'
        },
        gold: {
          DEFAULT: '#CDA434',
          50: '#fff9e6',
          100: '#ffefb3',
          200: '#ffe680',
          300: '#ffdc4d',
          400: '#FFD700',
          500: '#CDA434',
          600: '#b38f00',
          700: '#806600',
          800: '#4d3d00',
          900: '#1a1400',
          light: '#FFE55C',
          dark: '#8A6F00'
        },
        dark: {
          DEFAULT: '#333333',
          50: '#f5f5f5',
          100: '#e0e0e0',
          200: '#bdbdbd',
          300: '#9e9e9e',
          400: '#757575',
          500: '#616161',
          600: '#424242',
          700: '#303030',
          800: '#212121',
          900: '#121212',
          light: '#4a4a4a',
          dark: '#1a1a1a'
        }
      },
      fontFamily: {
        sans: ['var(--font-montserrat)', 'Montserrat', 'sans-serif']
      },
      backdropBlur: {
        xs: '2px'
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-in': 'bounceIn 0.5s ease-out'
      },
      boxShadow: {
        'glow': '0 0 15px rgba(46, 0, 62, 0.5)',
        'glow-lg': '0 0 25px rgba(46, 0, 62, 0.75)',
        'gold-glow': '0 0 15px rgba(205, 164, 52, 0.5)',
        'gold-glow-lg': '0 0 25px rgba(205, 164, 52, 0.75)'
      },
      backgroundImage: {
        'metallic-gold': 'linear-gradient(145deg, #FFE55C 0%, #CDA434 50%, #8A6F00 100%)',
        'metallic-gold-hover': 'linear-gradient(145deg, #FFE55C 0%, #E6C04D 50%, #B08C1E 100%)',
        'glass-effect': 'linear-gradient(135deg, rgba(46, 0, 62, 0.5) 0%, rgba(70, 0, 94, 0.4) 100%)',
        'glass-effect-light': 'linear-gradient(135deg, rgba(46, 0, 62, 0.3) 0%, rgba(70, 0, 94, 0.2) 100%)'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        },
        bounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        }
      }
    }
  },
  plugins: []
};

export default config;
